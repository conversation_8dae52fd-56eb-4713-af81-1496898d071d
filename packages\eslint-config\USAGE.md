# 快速开始指南

## 安装

```bash
# 安装配置包
pnpm add -D @components/eslint-config

# 安装必需的对等依赖
pnpm add -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
```

## 基础设置

### 1. TypeScript 项目

创建 `.eslintrc.js`：

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  parserOptions: {
    project: './tsconfig.json',
  },
};
```

### 2. Vue 3 项目

```javascript
module.exports = {
  extends: ['@components/eslint-config/vue'],
  parserOptions: {
    project: './tsconfig.json',
  },
};
```

### 3. React 项目

```javascript
module.exports = {
  extends: ['@components/eslint-config/react'],
  parserOptions: {
    project: './tsconfig.json',
  },
};
```

## Package.json 脚本

将这些添加到您的 `package.json` 中：

```json
{
  "scripts": {
    "lint": "eslint . --ext .js,.ts,.vue,.jsx,.tsx",
    "lint:fix": "eslint . --ext .js,.ts,.vue,.jsx,.tsx --fix"
  }
}
```

## 您将获得什么

✅ **实用规则**：专注于捕获真正的错误，而不是样式偏好
✅ **开发友好**：开发期间不对未使用的变量发出警告
✅ **现代 JavaScript**：支持最新的 ES 特性
✅ **TypeScript 优化**：与 TypeScript 配合良好的智能规则
✅ **框架支持**：为 Vue 3 和 React 提供专门的配置

## 与严格配置的主要区别

- **无未使用变量警告** - 自由开发，无需担心持续的警告
- **允许控制台语句** - 使用 `console.log` 进行调试
- **灵活的类型定义** - 在需要时允许使用 `any` 类型
- **实用优于完美** - 有助于而非阻碍开发的规则

## 测试

在您的项目中运行：

```bash
pnpm run lint
```

与严格的 ESLint 配置相比，您应该看到明显更少的错误，同时仍能捕获重要问题。
