# Quick Start Guide

## Installation

```bash
# Install the config package
pnpmadd -D @components/eslint-config

# Install required peer dependencies
pnpmadd -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
```

## Basic Setup

### 1. TypeScript Project

Create `.eslintrc.js`:

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  parserOptions: {
    project: './tsconfig.json',
  },
};
```

### 2. Vue 3 Project

```javascript
module.exports = {
  extends: ['@components/eslint-config/vue'],
  parserOptions: {
    project: './tsconfig.json',
  },
};
```

### 3. React Project

```javascript
module.exports = {
  extends: ['@components/eslint-config/react'],
  parserOptions: {
    project: './tsconfig.json',
  },
};
```

## Package.json Scripts

Add these to your `package.json`:

```json
{
  "scripts": {
    "lint": "eslint . --ext .js,.ts,.vue,.jsx,.tsx",
    "lint:fix": "eslint . --ext .js,.ts,.vue,.jsx,.tsx --fix"
  }
}
```

## What You Get

✅ **Practical Rules**: Focus on catching real bugs, not style preferences
✅ **Development Friendly**: No warnings for unused variables during development
✅ **Modern JavaScript**: Support for latest ES features
✅ **TypeScript Optimized**: Smart rules that work well with TypeScript
✅ **Framework Support**: Specialized configs for Vue 3 and React

## Key Differences from Strict Configs

- **No unused variable warnings** - Develop freely without constant warnings
- **Console statements allowed** - Use `console.log` for debugging
- **Flexible typing** - `any` type is allowed when needed
- **Practical over perfect** - Rules that help, not hinder development

## Testing

Run in your project:

```bash
pnpmrun lint
```

You should see significantly fewer errors compared to strict ESLint configs, while still catching important issues.
