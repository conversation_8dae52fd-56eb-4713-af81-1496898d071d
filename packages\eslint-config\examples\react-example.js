// Example .eslintrc.js for a React + TypeScript project

module.exports = {
  root: true,
  extends: ['@components/eslint-config/react'],
  parserOptions: {
    project: './tsconfig.json',
  },
  env: {
    browser: true,
    node: true,
    jest: true,
  },
  ignorePatterns: [
    'dist/',
    'build/',
    'node_modules/',
    '*.config.js',
  ],
  settings: {
    react: {
      version: 'detect',
    },
  },
  rules: {
    // React-specific rule overrides
    // Uncomment and modify as needed
    
    // 'react/jsx-props-no-spreading': 'error', // Disallow prop spreading
    // 'react-hooks/exhaustive-deps': 'error', // Make deps warnings errors
    // 'react/no-array-index-key': 'error', // Disallow array index as key
  },
  overrides: [
    {
      // TypeScript files
      files: ['*.ts', '*.tsx'],
      rules: {
        // TypeScript-specific overrides
      },
    },
    {
      // Test files
      files: [
        '**/*.test.{js,ts,jsx,tsx}',
        '**/*.spec.{js,ts,jsx,tsx}',
        '**/__tests__/**/*.{js,ts,jsx,tsx}',
      ],
      env: {
        jest: true,
      },
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'react/display-name': 'off',
        'react/prop-types': 'off',
      },
    },
    {
      // Storybook files
      files: ['**/*.stories.{js,ts,jsx,tsx}'],
      rules: {
        'react/jsx-props-no-spreading': 'off',
        '@typescript-eslint/no-explicit-any': 'off',
      },
    },
  ],
};
