<template>
  <div class="data-table-wrapper">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 表格 -->
    <table
      v-else
      class="data-table" :class="[
        {
          'border-0': !bordered,
          'data-table-striped': striped,
          'data-table-hoverable': hoverable
        }
      ]"
    >
      <!-- 表头 -->
      <thead v-if="showHeader">
        <tr>
          <th
            v-for="column in columns"
            :key="column.key"
            :style="{ width: column.width, textAlign: column.align || 'left' }"
            :class="{ 'cursor-pointer select-none': column.sortable }"
            @click="column.sortable && handleSort(column.key)"
          >
            <div class="flex items-center">
              {{ column.title }}
            </div>
          </th>
        </tr>
      </thead>

      <!-- 表体 -->
      <tbody>
        <tr v-for="(record, index) in paginatedData" :key="index">
          <td
            v-for="column in columns"
            :key="column.key"
            :style="{ textAlign: column.align || 'left' }"
          >
            <template v-if="column.render">
              <component
                :is="column.render(record[column.key], record, index)"
                v-if="typeof column.render(record[column.key], record, index) === 'object'"
              />
              <span v-else>{{ column.render(record[column.key], record, index) }}</span>
            </template>
            <span v-else>{{ record[column.key] }}</span>
          </td>
        </tr>

        <!-- 空数据提示 -->
        <tr v-if="data.length === 0">
          <td :colspan="columns.length" class="text-center py-8 text-gray-500">
            暂无数据
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 分页 -->
    <div v-if="pagination && data.length > pageSize" class="flex justify-center mt-4">
      <nav class="flex space-x-2">
        <button
          :disabled="currentPage === 1"
          class="px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          @click="currentPage > 1 && (currentPage--)"
        >
          上一页
        </button>

        <span class="px-3 py-1 border rounded bg-blue-50 text-blue-600">
          {{ currentPage }} / {{ totalPages }}
        </span>

        <button
          :disabled="currentPage === totalPages"
          class="px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          @click="currentPage < totalPages && (currentPage++)"
        >
          下一页
        </button>
      </nav>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { TableColumn, TableData, TableProps } from '../types'

// Props
const props = withDefaults(defineProps<TableProps>(), {
  loading: false,
  pagination: false,
  pageSize: 10,
  showHeader: true,
  bordered: true,
  striped: false,
  hoverable: true,
})

// 响应式数据
const currentPage = ref(1)
const sortKey = ref<string>('')
const sortOrder = ref<'asc' | 'desc'>('asc')

// 计算属性
const totalPages = computed(() => {
  if (!props.pagination) return 1
  return Math.ceil(props.data.length / props.pageSize)
})

const sortedData = computed(() => {
  if (!sortKey.value) return props.data

  return [...props.data].sort((a, b) => {
    const aVal = a[sortKey.value]
    const bVal = b[sortKey.value]

    if (aVal < bVal) return sortOrder.value === 'asc' ? -1 : 1
    if (aVal > bVal) return sortOrder.value === 'asc' ? 1 : -1
    return 0
  })
})

const paginatedData = computed(() => {
  if (!props.pagination) return sortedData.value

  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return sortedData.value.slice(start, end)
})

// 方法
const handleSort = (key: string) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
  currentPage.value = 1
}
</script>

<style scoped>
.data-table-striped tbody tr:nth-child(even) {
  @apply bg-gray-50;
}

.data-table-hoverable tbody tr:hover {
  @apply bg-blue-50;
}
</style>
