/**
 * TypeScript ESLint configuration with practical rules
 * Extends base config with TypeScript-specific rules
 */

import baseConfig from "./index.js";
import tseslint from "@typescript-eslint/eslint-plugin";
import tsparser from "@typescript-eslint/parser";

export default [
  // 继承基础配置
  ...baseConfig,

  // TypeScript 推荐配置
  {
    name: "@components/eslint-config/typescript",
    languageOptions: {
      parser: tsparser,
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        projectService: true,
      },
    },
    plugins: {
      "@typescript-eslint": tseslint,
    },
    rules: {
      // === Override base rules for TypeScript ===
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": "off", // Too strict for development

      "no-redeclare": "off",
      "@typescript-eslint/no-redeclare": "error",

      "no-shadow": "off",
      "@typescript-eslint/no-shadow": "off", // Can be useful

      // === TypeScript Specific ===
      "@typescript-eslint/no-explicit-any": "off", // Allow any for flexibility
      "@typescript-eslint/explicit-function-return-type": "off", // Too verbose
      "@typescript-eslint/explicit-module-boundary-types": "off", // Too strict
      "@typescript-eslint/no-inferrable-types": "warn",
      "@typescript-eslint/no-non-null-assertion": "warn", // Allow ! but warn
      "@typescript-eslint/no-empty-interface": "warn",
      "@typescript-eslint/no-empty-function": "off", // Allow empty functions

      // === Type Safety (Practical) ===
      "@typescript-eslint/no-unsafe-assignment": "off", // Too strict
      "@typescript-eslint/no-unsafe-member-access": "off", // Too strict
      "@typescript-eslint/no-unsafe-call": "off", // Too strict
      "@typescript-eslint/no-unsafe-return": "off", // Too strict
      "@typescript-eslint/no-unsafe-argument": "off", // Too strict

      // === Code Quality ===
      "@typescript-eslint/prefer-nullish-coalescing": "warn",
      "@typescript-eslint/prefer-optional-chain": "warn",
      "@typescript-eslint/prefer-as-const": "warn",
      "@typescript-eslint/no-unnecessary-type-assertion": "warn",
      "@typescript-eslint/no-duplicate-enum-values": "error",

      // === Style ===
      "@typescript-eslint/consistent-type-imports": [
        "warn",
        {
          prefer: "type-imports",
          disallowTypeAnnotations: false,
        },
      ],
      "@typescript-eslint/consistent-type-definitions": ["warn", "interface"],
      "@typescript-eslint/array-type": ["warn", { default: "array-simple" }],

      // === Naming Conventions (Light) ===
      "@typescript-eslint/naming-convention": [
        "warn",
        {
          selector: "interface",
          format: ["PascalCase"],
        },
        {
          selector: "typeAlias",
          format: ["PascalCase"],
        },
        {
          selector: "enum",
          format: ["PascalCase"],
        },
      ],

      // === Functions ===
      "@typescript-eslint/prefer-function-type": "warn",
      "@typescript-eslint/method-signature-style": ["warn", "property"],

      // === Promises ===
      "@typescript-eslint/no-floating-promises": "off", // Too strict for many cases
      "@typescript-eslint/no-misused-promises": "off", // Too strict

      // === Performance ===
      "@typescript-eslint/prefer-readonly": "off", // Nice to have but not critical
      "@typescript-eslint/prefer-readonly-parameter-types": "off", // Too strict
    },
  },
];
