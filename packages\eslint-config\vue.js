/**
 * Vue + TypeScript ESLint configuration
 * Extends TypeScript config with Vue-specific rules
 */

import typescriptConfig from "./typescript.js";
import pluginVue from "eslint-plugin-vue";

export default [
  // 继承 TypeScript 配置
  ...typescriptConfig,

  // Vue 3 推荐配置
  ...pluginVue.configs["flat/recommended"],

  {
    name: "@components/eslint-config/vue",
    languageOptions: {
      parser: pluginVue.parser,
      parserOptions: {
        parser: "@typescript-eslint/parser",
        ecmaVersion: "latest",
        sourceType: "module",
        projectService: true,
        extraFileExtensions: [".vue"],
      },
    },
    plugins: {
      vue: pluginVue,
    },
    rules: {
      // === Vue Template ===
      "vue/html-indent": ["warn", 2],
      "vue/html-closing-bracket-newline": "off", // Let prettier handle
      "vue/html-self-closing": [
        "warn",
        {
          html: {
            void: "always",
            normal: "always",
            component: "always",
          },
          svg: "always",
          math: "always",
        },
      ],
      "vue/max-attributes-per-line": "off", // Let prettier handle
      "vue/singleline-html-element-content-newline": "off",
      "vue/multiline-html-element-content-newline": "off",

      // === Vue Script ===
      "vue/script-setup-uses-vars": "error",
      "vue/no-unused-vars": "off", // Too strict for development
      "vue/no-unused-components": "warn",
      "vue/no-unused-refs": "warn",

      // === Vue Composition API ===
      "vue/prefer-import-from-vue": "warn",
      "vue/prefer-separate-static-class": "warn",
      "vue/prefer-true-attribute-shorthand": "warn",
      "vue/component-api-style": ["warn", ["script-setup"]],
      "vue/component-name-in-template-casing": ["warn", "PascalCase"],
      "vue/custom-event-name-casing": ["warn", "camelCase"],

      // === Vue Best Practices ===
      "vue/no-v-html": "warn", // Security concern but not error
      "vue/require-default-prop": "off", // Not needed with TypeScript
      "vue/require-prop-types": "off", // Not needed with TypeScript
      "vue/prop-name-casing": ["warn", "camelCase"],
      "vue/attribute-hyphenation": ["warn", "always"],
      "vue/v-on-event-hyphenation": ["warn", "always"],

      // === Vue Style ===
      "vue/block-order": [
        "warn",
        {
          order: ["template", "script", "style"],
        },
      ],
      "vue/component-tags-order": [
        "warn",
        {
          order: ["template", "script", "style"],
        },
      ],
      "vue/define-macros-order": [
        "warn",
        {
          order: ["defineOptions", "defineProps", "defineEmits", "defineSlots"],
        },
      ],

      // === Vue Performance ===
      "vue/no-setup-props-destructure": "error", // Important for reactivity
      "vue/no-ref-as-operand": "error",
      "vue/no-watch-after-await": "warn",

      // === Vue Template Directives ===
      "vue/no-multiple-template-root": "off", // Vue 3 allows multiple roots
      "vue/no-v-for-template-key": "off", // Vue 3 syntax
      "vue/no-v-model-argument": "off", // Vue 3 feature
      "vue/valid-v-slot": [
        "error",
        {
          allowModifiers: true,
        },
      ],

      // === Accessibility (Light) ===
      "vue/require-toggle-inside-transition": "warn",
      "vue/no-static-inline-styles": "off", // Sometimes needed

      // === Vue 3 Specific ===
      "vue/prefer-define-options": "warn",
      "vue/define-props-declaration": ["warn", "type-based"],
      "vue/define-emits-declaration": ["warn", "type-based"],
      "vue/enforce-style-attribute": "off", // Allow both scoped and module

      // === Relaxed Rules ===
      "vue/multi-word-component-names": "off", // Too restrictive
      "vue/no-reserved-component-names": "warn", // Warn instead of error
    },
  },
];
