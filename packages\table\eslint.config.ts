// @ts-check
import vueConfig from '@components/eslint-config/vue.js'
import pluginVitest from '@vitest/eslint-plugin'
import pluginPlaywright from 'eslint-plugin-playwright'

export default [
  {
    name: 'table/ignores',
    ignores: [
      '**/dist/**',
      '**/dist-ssr/**',
      '**/coverage/**',
      '**/node_modules/**',
      '*.config.js',
      '*.config.mjs',
      'postcss.config.js',
      'tailwind.config.js',
    ],
  },

  // 使用我们的 Vue + TypeScript 配置
  ...vueConfig,

  // Vitest 测试配置
  {
    name: 'table/vitest',
    files: ['src/**/__tests__/**/*', 'src/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    ...pluginVitest.configs.recommended,
  },

  // Playwright E2E 测试配置
  {
    name: 'table/playwright',
    files: ['e2e/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    ...pluginPlaywright.configs['flat/recommended'],
  },

  // 项目特定的规则覆盖
  {
    name: 'table/overrides',
    rules: {
      // 组件库特定的规则
      'vue/multi-word-component-names': 'off', // 组件库可能有单词组件名
      '@typescript-eslint/no-explicit-any': 'warn', // 组件库中稍微严格一些
    },
  },

  // 测试文件的特殊规则
  {
    name: 'table/test-overrides',
    files: ['**/*.{test,spec}.{js,ts,jsx,tsx}', 'e2e/**/*'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      'no-console': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
    },
  },
]
