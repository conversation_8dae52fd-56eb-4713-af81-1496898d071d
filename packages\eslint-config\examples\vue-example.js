// Example .eslintrc.js for a Vue 3 + TypeScript project

module.exports = {
  root: true,
  extends: ['@components/eslint-config/vue'],
  parserOptions: {
    project: './tsconfig.json',
    extraFileExtensions: ['.vue'],
  },
  env: {
    browser: true,
    node: true,
  },
  ignorePatterns: [
    'dist/',
    'node_modules/',
    '*.config.js',
  ],
  rules: {
    // Vue-specific rule overrides
    // Uncomment and modify as needed
    
    // 'vue/multi-word-component-names': 'error', // Enforce multi-word component names
    // 'vue/no-unused-components': 'error', // Make unused components an error
    // 'vue/component-name-in-template-casing': ['error', 'kebab-case'], // Use kebab-case
  },
  overrides: [
    {
      // TypeScript files
      files: ['*.ts', '*.tsx'],
      rules: {
        // TypeScript-specific overrides for .ts files
      },
    },
    {
      // Vue files
      files: ['*.vue'],
      rules: {
        // Vue-specific overrides
        'vue/script-setup-uses-vars': 'error',
      },
    },
    {
      // Test files
      files: ['**/*.test.{js,ts,vue}', '**/*.spec.{js,ts,vue}'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'vue/one-component-per-file': 'off',
      },
    },
  ],
};
