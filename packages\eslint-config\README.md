# @components/eslint-config

A practical ESLint configuration for TypeScript projects that focuses on code quality and consistency without being overly strict.

## Philosophy

This ESLint configuration is designed with the following principles:

- **Practical over Perfect**: Rules that help catch real bugs and improve code quality
- **Developer-Friendly**: No overly strict rules that interrupt development flow
- **Flexible**: Allow common patterns and don't enforce overly opinionated styles
- **Modern**: Support for latest JavaScript/TypeScript features and frameworks

## Key Features

- ✅ **Unused variables allowed** - No warnings for unused imports/variables during development
- ✅ **Flexible typing** - Allow `any` type when needed
- ✅ **Console statements** - Allow `console.log` with warnings only
- ✅ **Modern syntax** - Support for ES2022+ features
- ✅ **Framework support** - Specialized configs for Vue 3 and React
- ✅ **TypeScript first** - Optimized for TypeScript development

## Installation

```bash
# Using pnpm(recommended)
pnpmadd -D @components/eslint-config

# Using npm
npm install --save-dev @components/eslint-config

# Using yarn
yarn add -D @components/eslint-config

# Using pnpm
pnpm add -D @components/eslint-config
```

### Peer Dependencies

You'll also need to install the required peer dependencies:

```bash
# For TypeScript projects
pnpmadd -D eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser

# For Vue projects (additional)
pnpmadd -D eslint-plugin-vue

# For React projects (additional)
pnpmadd -D eslint-plugin-react eslint-plugin-react-hooks
```

## Usage

### Basic JavaScript/TypeScript

Create an `.eslintrc.js` file in your project root:

```javascript
module.exports = {
  extends: ['@components/eslint-config'],
  // Your custom rules here
};
```

### TypeScript Projects

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  parserOptions: {
    project: './tsconfig.json',
  },
  // Your custom rules here
};
```

### Vue 3 + TypeScript

```javascript
module.exports = {
  extends: ['@components/eslint-config/vue'],
  parserOptions: {
    project: './tsconfig.json',
  },
  // Your custom rules here
};
```

### React + TypeScript

```javascript
module.exports = {
  extends: ['@components/eslint-config/react'],
  parserOptions: {
    project: './tsconfig.json',
  },
  // Your custom rules here
};
```

## Available Configurations

| Configuration                          | Description           | Use Case          |
| -------------------------------------- | --------------------- | ----------------- |
| `@components/eslint-config`            | Base JavaScript rules | Basic JS projects |
| `@components/eslint-config/typescript` | TypeScript rules      | TS projects       |
| `@components/eslint-config/vue`        | Vue 3 + TypeScript    | Vue 3 projects    |
| `@components/eslint-config/react`      | React + TypeScript    | React projects    |

## Rule Highlights

### What's Allowed (Unlike Strict Configs)

- ✅ Unused variables and imports
- ✅ `console.log` statements (with warnings)
- ✅ `any` type in TypeScript
- ✅ Non-null assertions (`!`)
- ✅ Empty functions
- ✅ Magic numbers
- ✅ Single-word component names in Vue

### What's Enforced

- ❌ `eval()` and similar dangerous functions
- ❌ `var` declarations (use `const`/`let`)
- ❌ Duplicate object keys
- ❌ Unreachable code
- ❌ Invalid typeof comparisons
- ❌ React Hooks rules violations

## Package Scripts

Add these scripts to your `package.json`:

```json
{
  "scripts": {
    "lint": "eslint . --ext .js,.ts,.vue,.jsx,.tsx",
    "lint:fix": "eslint . --ext .js,.ts,.vue,.jsx,.tsx --fix"
  }
}
```

## Advanced Configuration

### Custom Rules

You can override any rule in your project:

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  rules: {
    // Make unused vars an error instead of off
    '@typescript-eslint/no-unused-vars': 'error',

    // Disable console warnings
    'no-console': 'off',

    // Add your custom rules
    'prefer-const': 'error',
  },
};
```

### Environment-Specific Configs

```javascript
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  env: {
    browser: true,
    node: true,
    jest: true,
  },
  overrides: [
    {
      // Stricter rules for production files
      files: ['src/**/*.ts'],
      rules: {
        'no-console': 'error',
        '@typescript-eslint/no-unused-vars': 'error',
      },
    },
    {
      // Relaxed rules for test files
      files: ['**/*.test.ts', '**/*.spec.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off',
      },
    },
  ],
};
```

### Monorepo Setup

For monorepos, you can use different configs for different packages:

```javascript
// Root .eslintrc.js
module.exports = {
  extends: ['@components/eslint-config/typescript'],
  overrides: [
    {
      files: ['packages/vue-app/**/*.{ts,vue}'],
      extends: ['@components/eslint-config/vue'],
    },
    {
      files: ['packages/react-app/**/*.{ts,tsx}'],
      extends: ['@components/eslint-config/react'],
    },
  ],
};
```

## Integration with Tools

### VS Code

Install the ESLint extension and add to your `.vscode/settings.json`:

```json
{
  "eslint.validate": [
    "javascript",
    "typescript",
    "vue",
    "javascriptreact",
    "typescriptreact"
  ],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### Prettier Integration

This config is designed to work with Prettier. Install prettier and create `.prettierrc`:

```json
{
  "semi": true,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5"
}
```

### Pre-commit Hooks

Using husky and lint-staged:

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,ts,vue,jsx,tsx}": ["eslint --fix", "git add"]
  }
}
```

## Migration Guide

### From Standard Configs

If migrating from `@typescript-eslint/recommended` or similar strict configs:

1. Remove the old config from your extends array
2. Add `@components/eslint-config/typescript`
3. Remove any rule overrides that are now unnecessary
4. Test your build - you should see fewer linting errors

### From No Linting

If adding linting to an existing project:

1. Install the package and peer dependencies
2. Start with the base config for your framework
3. Run `npm run lint` to see current issues
4. Fix critical errors first (usually actual bugs)
5. Gradually address warnings as time permits

## Contributing

Found a rule that's too strict or too lenient? Please open an issue with:

- The rule name
- Why it should be changed
- Example code that demonstrates the issue
- Suggested new setting

## License

MIT
