import vueConfig from "@components/eslint-config/vue.js";

export default [
  {
    name: "storybook/ignores",
    ignores: [
      "**/node_modules/**",
      "**/storybook-static/**",
      "**/dist/**",
      "eslint.config.js", // 忽略自己
    ],
  },

  // 使用我们的 Vue + TypeScript 配置
  ...vueConfig,

  {
    name: "storybook/overrides",
    rules: {
      // Storybook 特定的规则覆盖
      "vue/multi-word-component-names": "off", // Stories 可能使用单词组件名
      "@typescript-eslint/no-explicit-any": "off", // Stories 中可能需要 any
      "no-console": "off", // Storybook 中允许 console
      "no-alert": "off", // Storybook 演示中可能需要 alert

      // 允许在 stories 中使用更灵活的规则
      "@typescript-eslint/no-unused-vars": "off",
      "vue/no-unused-components": "off",
      "no-duplicate-imports": "warn", // 降级为警告
      "no-trailing-spaces": "warn", // 降级为警告
      "vue/attributes-order": "warn", // 降级为警告
      "vue/html-self-closing": "warn", // 降级为警告
      "vue/prefer-true-attribute-shorthand": "warn", // 降级为警告
      "@typescript-eslint/prefer-nullish-coalescing": "warn", // 降级为警告
    },
  },
];
