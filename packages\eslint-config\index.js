/**
 * Base ESLint configuration with practical rules
 * Focuses on code quality and consistency without being overly strict
 */

import js from "@eslint/js";

export default [
  // ESLint 推荐配置
  js.configs.recommended,

  {
    name: "@components/eslint-config/base",
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      globals: {
        // Browser globals
        window: "readonly",
        document: "readonly",
        console: "readonly",
        // Node.js globals
        process: "readonly",
        Buffer: "readonly",
        __dirname: "readonly",
        __filename: "readonly",
        global: "readonly",
        module: "readonly",
        require: "readonly",
        exports: "readonly",
      },
    },
    rules: {
      // === Code Quality ===
      "no-console": "warn", // Allow console but warn
      "no-debugger": "warn", // Allow debugger in development
      "no-alert": "warn",
      "no-eval": "error",
      "no-implied-eval": "error",
      "no-new-func": "error",

      // === Variables ===
      "no-unused-vars": "off", // Disabled - too strict for development
      "no-undef": "error",
      "no-redeclare": "error",
      "no-shadow": "off", // Can be useful in some cases

      // === Best Practices ===
      eqeqeq: ["warn", "always"], // Prefer === but not error
      curly: ["warn", "multi-line"], // Require braces for multi-line
      "no-empty": "warn",
      "no-empty-function": "off", // Allow empty functions
      "no-magic-numbers": "off", // Too restrictive
      "prefer-const": "warn",
      "no-var": "error",

      // === Style (Light) ===
      indent: "off", // Let prettier handle this
      quotes: "off", // Let prettier handle this
      semi: "off", // Let prettier handle this
      "comma-dangle": "off", // Let prettier handle this
      "no-trailing-spaces": "warn",
      "no-multiple-empty-lines": ["warn", { max: 2 }],

      // === ES6+ ===
      "arrow-spacing": "warn",
      "no-duplicate-imports": "warn",
      "prefer-template": "warn",
      "template-curly-spacing": "warn",

      // === Functions ===
      "no-unused-expressions": "off", // Allow for short-circuit evaluation
      "consistent-return": "off", // Too strict
      "no-return-assign": "warn",

      // === Objects & Arrays ===
      "dot-notation": "warn",
      "no-useless-computed-key": "warn",
      "object-shorthand": "warn",

      // === Error Prevention ===
      "no-unreachable": "error",
      "no-constant-condition": "warn",
      "no-dupe-keys": "error",
      "no-duplicate-case": "error",
      "valid-typeof": "error",
    },
  },
];
