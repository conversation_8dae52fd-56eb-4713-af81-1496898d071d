{"name": "@components/eslint-config", "version": "1.0.0", "description": "Shared ESLint configuration for TypeScript projects with practical rules", "main": "index.js", "type": "module", "keywords": ["eslint", "eslint-config", "typescript", "javascript", "vue", "react"], "author": "Components Team", "license": "MIT", "files": ["index.js", "typescript.js", "vue.js", "react.js", "README.md"], "peerDependencies": {"eslint": "^8.0.0 || ^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0 || ^7.0.0 || ^8.0.0", "@typescript-eslint/parser": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"@typescript-eslint/eslint-plugin": {"optional": false}, "@typescript-eslint/parser": {"optional": false}}, "devDependencies": {"eslint": "^9.29.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint-plugin-vue": "^9.30.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "typescript": "^5.8.0"}, "scripts": {"lint": "eslint *.js", "test": "node test.js", "prepublishOnly": "npm run lint && npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/components.git", "directory": "packages/eslint-config"}, "bugs": {"url": "https://github.com/your-org/components/issues"}, "homepage": "https://github.com/your-org/components/tree/main/packages/eslint-config#readme", "engines": {"node": ">=18.0.0"}}