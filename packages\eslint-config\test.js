#!/usr/bin/env node

/**
 * Simple test to verify ESLint configurations can be loaded
 */

import { readFileSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __dirname = dirname(fileURLToPath(import.meta.url));

async function testConfig(configName) {
  try {
    console.log(`Testing ${configName} config...`);

    // Try to import the config
    const configPath = new URL(`${configName}.js`, import.meta.url);
    const config = await import(configPath);

    // Basic validation
    if (!config.default) {
      throw new Error(`${configName} config does not export default`);
    }

    const configObj = config.default;

    // Check if it has rules
    if (!configObj.rules || typeof configObj.rules !== "object") {
      throw new Error(`${configName} config does not have rules object`);
    }

    const ruleCount = Object.keys(configObj.rules).length;
    console.log(`✅ ${configName}: ${ruleCount} rules configured`);

    return true;
  } catch (error) {
    console.error(`❌ ${configName}: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log("🧪 Testing ESLint configurations...\n");

  const configs = ["index", "typescript", "vue", "react"];
  const results = [];

  for (const config of configs) {
    const result = await testConfig(config);
    results.push(result);
  }

  const passed = results.filter(Boolean).length;
  const total = results.length;

  console.log(`\n📊 Test Results: ${passed}/${total} configurations passed`);

  if (passed === total) {
    console.log("🎉 All tests passed!");
    process.exit(0);
  } else {
    console.log("💥 Some tests failed!");
    process.exit(1);
  }
}

// Validate package.json
try {
  const packageJson = JSON.parse(
    readFileSync(join(__dirname, "package.json"), "utf8")
  );
  console.log(`📦 Package: ${packageJson.name}@${packageJson.version}`);
  console.log(`📝 Description: ${packageJson.description}\n`);
} catch (error) {
  console.error("❌ Failed to read package.json:", error.message);
  process.exit(1);
}

runTests().catch(console.error);
