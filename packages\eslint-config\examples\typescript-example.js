// Example .eslintrc.js for a TypeScript project

module.exports = {
  root: true,
  extends: ['@components/eslint-config/typescript'],
  parserOptions: {
    project: './tsconfig.json',
  },
  env: {
    browser: true,
    node: true,
  },
  ignorePatterns: [
    'dist/',
    'node_modules/',
    '*.config.js',
  ],
  rules: {
    // Project-specific rule overrides
    // Uncomment and modify as needed
    
    // '@typescript-eslint/no-unused-vars': 'error', // Make unused vars an error
    // 'no-console': 'off', // Allow console statements
    // '@typescript-eslint/no-explicit-any': 'warn', // Warn on any usage
  },
  overrides: [
    {
      // Test files can be more relaxed
      files: ['**/*.test.ts', '**/*.spec.ts', '**/__tests__/**/*.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off',
      },
    },
    {
      // Config files
      files: ['*.config.{js,ts}', '.eslintrc.js'],
      env: {
        node: true,
      },
      rules: {
        'no-console': 'off',
      },
    },
  ],
};
